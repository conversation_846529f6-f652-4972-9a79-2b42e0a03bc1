<?php

namespace LBCDev\OAuthFileExplorer\Tests\Unit;

use LBCDev\OAuthFileExplorer\Tests\TestCase;
use LBC<PERSON>ev\OAuthFileExplorer\Services\FileExplorerFactory;
use LBCDev\OAuthFileExplorer\Services\Explorers\GoogleDriveExplorer;
use LBCDev\OAuthFileExplorer\Services\Explorers\DropboxExplorer;
use LBCDev\OAuthFileExplorer\Services\Explorers\OneDriveExplorer;
use LBCDev\OAuthManager\Models\OAuthService;
use InvalidArgumentException;
use PHPUnit\Framework\Attributes\Test;

class FileExplorerFactoryTest extends TestCase
{
    #[Test]
    public function it_creates_google_drive_explorer()
    {
        $oauthService = OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        $explorer = FileExplorerFactory::create($oauthService);

        $this->assertInstanceOf(GoogleDriveExplorer::class, $explorer);
        $this->assertEquals('google_drive', $explorer->getServiceType());
    }

    #[Test]
    public function it_creates_dropbox_explorer()
    {
        $oauthService = OAuthService::create([
            'name' => 'Test Dropbox',
            'service_type' => 'dropbox',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        $explorer = FileExplorerFactory::create($oauthService);

        $this->assertInstanceOf(DropboxExplorer::class, $explorer);
        $this->assertEquals('dropbox', $explorer->getServiceType());
    }

    #[Test]
    public function it_creates_onedrive_explorer()
    {
        $oauthService = OAuthService::create([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        $explorer = FileExplorerFactory::create($oauthService);

        $this->assertInstanceOf(OneDriveExplorer::class, $explorer);
        $this->assertEquals('onedrive', $explorer->getServiceType());
    }

    #[Test]
    public function it_throws_exception_for_unsupported_service_type()
    {
        $oauthService = OAuthService::create([
            'name' => 'Test Unsupported',
            'service_type' => 'unsupported_service',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Unsupported service type: unsupported_service');

        FileExplorerFactory::create($oauthService);
    }

    #[Test]
    public function it_creates_explorer_by_type()
    {
        OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        $explorer = FileExplorerFactory::createByType('google_drive');

        $this->assertInstanceOf(GoogleDriveExplorer::class, $explorer);
    }

    #[Test]
    public function it_throws_exception_when_no_active_service_found()
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage("No active OAuth service found for type 'google_drive'");

        FileExplorerFactory::createByType('google_drive');
    }

    #[Test]
    public function it_returns_supported_service_types()
    {
        $supportedTypes = FileExplorerFactory::getSupportedServiceTypes();

        $this->assertIsArray($supportedTypes);
        $this->assertContains('google_drive', $supportedTypes);
        $this->assertContains('dropbox', $supportedTypes);
    }

    #[Test]
    public function it_checks_if_service_type_is_supported()
    {
        $this->assertTrue(FileExplorerFactory::isServiceTypeSupported('google_drive'));
        $this->assertTrue(FileExplorerFactory::isServiceTypeSupported('dropbox'));
        $this->assertFalse(FileExplorerFactory::isServiceTypeSupported('unsupported'));
    }

    #[Test]
    public function it_gets_available_services()
    {
        // Create active service
        OAuthService::create([
            'name' => 'Active Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        // Create inactive service
        OAuthService::create([
            'name' => 'Inactive Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'test_token',
            'is_active' => false,
        ]);

        // Create service without token
        OAuthService::create([
            'name' => 'No Token Google Drive',
            'service_type' => 'google_drive',
            'access_token' => null,
            'is_active' => true,
        ]);

        $availableServices = FileExplorerFactory::getAvailableServices();

        $this->assertCount(1, $availableServices);
        $this->assertEquals('Active Google Drive', $availableServices->first()->name);
    }
}
