<?php
    $oauthServiceField = $getOauthServiceField();
    $oauthServiceId = null;
    $serviceType = null;
    
    if ($oauthServiceField) {
        $oauthServiceId = data_get($getRecord(), $oauthServiceField) ?? $this->form->getRawState()[$oauthServiceField] ?? null;
        
        // Get service type from the OAuth service
        if ($oauthServiceId) {
            $oauthService = \LBCDev\OAuthManager\Models\OAuthService::find($oauthServiceId);
            $serviceType = $oauthService?->service_type;
        }
    }
    
    // Get service display name
    $serviceDisplayName = match ($serviceType) {
        'google_drive' => 'Google Drive',
        'dropbox' => 'Dropbox',
        'onedrive' => 'OneDrive',
        'youtube' => 'YouTube',
        'mega' => 'Mega',
        default => ucfirst(str_replace('_', ' ', $serviceType ?? 'Cloud Storage'))
    };

    $statePath = $getStatePath();
    $currentValue = $getState() ?? '';
?>

<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <div class="space-y-4"
         x-data="{
             init() {
                 // Escuchar eventos de selección de archivo
                 const eventName = 'fileSelected-<?php echo e(str_replace('.', '-', $statePath)); ?>';
                 console.log('Listening for event:', eventName);
                 
                 window.addEventListener(eventName, (event) => {
                     console.log('File selected event received:', event.detail.fileUrl);
                     this.updateFieldValue(event.detail.fileUrl);
                 });
             },
             updateFieldValue(value) {
                 console.log('Updating field value:', value, 'to path:', '<?php echo e($statePath); ?>');
                 
                 try {
                     // Método 1: Actualizar el input directamente
                     if (this.$refs.fileUrlInput) {
                         this.$refs.fileUrlInput.value = value;
                         this.$refs.fileUrlInput.dispatchEvent(new Event('input'));
                         console.log('Input updated directly');
                     }
                     
                     // Método 2: Usar window.Livewire.find('<?php echo e($_instance->getId()); ?>').set como respaldo
                     window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('<?php echo e($statePath); ?>', value);
                     console.log('Livewire state updated');
                     
                 } catch (error) {
                     console.error('Error updating field:', error);
                 }
             }
         }">

        
        <input
            type="text"
            wire:model.live="<?php echo e($statePath); ?>"
            x-ref="fileUrlInput"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="File URL will appear here when selected..."
            readonly
        />

        
        <!--[if BLOCK]><![endif]--><?php if($currentValue): ?>
            <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 13.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-900">Selected file:</span>
                    </div>
                    <button
                        type="button"
                        wire:click="$set('<?php echo e($statePath); ?>', '')"
                        class="text-red-600 hover:text-red-800"
                        title="Clear selection"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="mt-2">
                    <a 
                        href="<?php echo e($currentValue); ?>" 
                        target="_blank" 
                        class="text-sm text-blue-600 hover:text-blue-800 hover:underline break-all"
                    >
                        <?php echo e($currentValue); ?>

                    </a>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <!--[if BLOCK]><![endif]--><?php if($oauthServiceId): ?>
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('oauth-file-explorer', [
                'oauthServiceId' => $oauthServiceId,
                'fieldName' => $statePath,
                'acceptedMimeTypes' => $getAcceptedMimeTypes()
            ]);

$__html = app('livewire')->mount($__name, $__params, $statePath . '-' . $oauthServiceId, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        <?php else: ?>
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800">
                            <?php echo e($serviceDisplayName); ?> service required
                        </p>
                        <p class="text-xs text-yellow-700 mt-1">
                            <!--[if BLOCK]><![endif]--><?php if($oauthServiceField): ?>
                                Please select a <?php echo e($serviceDisplayName); ?> service in the "<?php echo e($oauthServiceField); ?>" field first.
                            <?php else: ?>
                                Please configure the OAuth service field for this file explorer.
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <!--[if BLOCK]><![endif]--><?php if(!$currentValue && $oauthServiceId): ?>
            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-blue-800">How to use:</p>
                        <ul class="text-xs text-blue-700 mt-1 space-y-1">
                            <li>• Browse folders by clicking on them</li>
                            <li>• Click on a file to select it</li>
                            <li>• Use the search bar to find specific files</li>
                            <li>• Use breadcrumbs to navigate back to parent folders</li>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

        
        <!--[if BLOCK]><![endif]--><?php if(app()->environment('local')): ?>
            <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-100 rounded">
                <strong>Debug:</strong><br>
                State Path: <?php echo e($statePath); ?><br>
                Current Value: <?php echo e($currentValue ?: 'empty'); ?><br>
                OAuth Service ID: <?php echo e($oauthServiceId ?: 'none'); ?><br>
                Expected Event Name: fileSelected-<?php echo e(str_replace('.', '-', $statePath)); ?><br>
                
                <button type="button" 
                        onclick="window.dispatchEvent(new CustomEvent('fileSelected-<?php echo e(str_replace('.', '-', $statePath)); ?>', { detail: { fileUrl: 'https://test.com/test.pdf' } }))"
                        class="mt-1 px-2 py-1 bg-blue-500 text-white text-xs rounded">
                    Test Event
                </button>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?><?php /**PATH C:\Proyectos\Clientes\Transition\Quantum\quantum_webapp\resources\views/filament/forms/components/oauth-file-explorer.blade.php ENDPATH**/ ?>