@php
    $oauthServiceField = $getOauthServiceField();
    $oauthServiceId = null;
    $serviceType = null;
    
    if ($oauthServiceField) {
        $oauthServiceId = data_get($getRecord(), $oauthServiceField) ?? $this->form->getRawState()[$oauthServiceField] ?? null;
        
        // Get service type from the OAuth service
        if ($oauthServiceId) {
            $oauthService = \LBCDev\OAuthManager\Models\OAuthService::find($oauthServiceId);
            $serviceType = $oauthService?->service_type;
        }
    }
    
    // Get service display name
    $serviceDisplayName = match ($serviceType) {
        'google_drive' => 'Google Drive',
        'dropbox' => 'Dropbox',
        'onedrive' => 'OneDrive',
        'youtube' => 'YouTube',
        'mega' => 'Mega',
        default => ucfirst(str_replace('_', ' ', $serviceType ?? 'Cloud Storage'))
    };

    $statePath = $getStatePath();
    $currentValue = $getState() ?? '';
@endphp

<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
>
    <div class="space-y-4"
         x-data="{
             init() {
                 // Escuchar eventos de selección de archivo
                 const eventName = 'fileSelected-{{ str_replace('.', '-', $statePath) }}';
                 console.log('Listening for event:', eventName);
                 
                 window.addEventListener(eventName, (event) => {
                     console.log('File selected event received:', event.detail.fileUrl);
                     this.updateFieldValue(event.detail.fileUrl);
                 });
             },
             updateFieldValue(value) {
                 console.log('Updating field value:', value, 'to path:', '{{ $statePath }}');
                 
                 try {
                     // Método 1: Actualizar el input directamente
                     if (this.$refs.fileUrlInput) {
                         this.$refs.fileUrlInput.value = value;
                         this.$refs.fileUrlInput.dispatchEvent(new Event('input'));
                         console.log('Input updated directly');
                     }
                     
                     // Método 2: Usar @this.set como respaldo
                     @this.set('{{ $statePath }}', value);
                     console.log('Livewire state updated');
                     
                 } catch (error) {
                     console.error('Error updating field:', error);
                 }
             }
         }">

        {{-- Input principal que maneja el estado --}}
        <input
            type="text"
            wire:model.live="{{ $statePath }}"
            x-ref="fileUrlInput"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="File URL will appear here when selected..."
            readonly
        />

        {{-- Current selected file display --}}
        @if($currentValue)
            <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 13.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium text-gray-900">Selected file:</span>
                    </div>
                    <button
                        type="button"
                        wire:click="$set('{{ $statePath }}', '')"
                        class="text-red-600 hover:text-red-800"
                        title="Clear selection"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="mt-2">
                    <a 
                        href="{{ $currentValue }}" 
                        target="_blank" 
                        class="text-sm text-blue-600 hover:text-blue-800 hover:underline break-all"
                    >
                        {{ $currentValue }}
                    </a>
                </div>
            </div>
        @endif

        {{-- OAuth File Explorer Component --}}
        @if($oauthServiceId)
            @livewire('oauth-file-explorer', [
                'oauthServiceId' => $oauthServiceId,
                'fieldName' => $statePath,
                'acceptedMimeTypes' => $getAcceptedMimeTypes()
            ], key($statePath . '-' . $oauthServiceId))
        @else
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-yellow-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-yellow-800">
                            {{ $serviceDisplayName }} service required
                        </p>
                        <p class="text-xs text-yellow-700 mt-1">
                            @if($oauthServiceField)
                                Please select a {{ $serviceDisplayName }} service in the "{{ $oauthServiceField }}" field first.
                            @else
                                Please configure the OAuth service field for this file explorer.
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        @endif

        {{-- Instructions --}}
        @if(!$currentValue && $oauthServiceId)
            <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-blue-800">How to use:</p>
                        <ul class="text-xs text-blue-700 mt-1 space-y-1">
                            <li>• Browse folders by clicking on them</li>
                            <li>• Click on a file to select it</li>
                            <li>• Use the search bar to find specific files</li>
                            <li>• Use breadcrumbs to navigate back to parent folders</li>
                        </ul>
                    </div>
                </div>
            </div>
        @endif

        {{-- Debug information (remove in production) --}}
        @if(app()->environment('local'))
            <div class="text-xs text-gray-500 mt-2 p-2 bg-gray-100 rounded">
                <strong>Debug:</strong><br>
                State Path: {{ $statePath }}<br>
                Current Value: {{ $currentValue ?: 'empty' }}<br>
                OAuth Service ID: {{ $oauthServiceId ?: 'none' }}<br>
                Expected Event Name: fileSelected-{{ str_replace('.', '-', $statePath) }}<br>
                
                <button type="button" 
                        onclick="window.dispatchEvent(new CustomEvent('fileSelected-{{ str_replace('.', '-', $statePath) }}', { detail: { fileUrl: 'https://test.com/test.pdf' } }))"
                        class="mt-1 px-2 py-1 bg-blue-500 text-white text-xs rounded">
                    Test Event
                </button>
            </div>
        @endif
    </div>
</x-dynamic-component>