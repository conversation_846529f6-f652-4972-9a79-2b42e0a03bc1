<?php

return [
    'services' => [
        'google_drive' => [
            'name' => 'Google Drive',
            'slug' => 'google-drive',
            'icon' => 'heroicon-o-folder',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => [
                // 'https://www.googleapis.com/auth/drive.file',
                // 'https://www.googleapis.com/auth/drive.metadata.readonly'
                'https://www.googleapis.com/auth/drive.readonly'
            ],
            'fields' => [
                'client_id' => env('GOOGLE_DRIVE_CLIENT_ID'),
                'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('GOOGLE_DRIVE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('GOOGLE_DRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        'onedrive' => [
            'name' => 'OneDrive',
            'slug' => 'onedrive',
            'icon' => 'heroicon-o-cloud',
            'provider' => \LBCDev\OAuthManager\Providers\OneDriveProvider::class,
            'scopes' => [
                'https://graph.microsoft.com/Files.Read',
                'https://graph.microsoft.com/Files.ReadWrite',
                'https://graph.microsoft.com/User.Read'
            ],
            'fields' => [
                'client_id' => env('ONEDRIVE_CLIENT_ID'),
                'client_secret' => env('ONEDRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('ONEDRIVE_REDIRECT_URI'),
            ]
        ],
        'dropbox' => [
            'name' => 'Dropbox',
            'slug' => 'dropbox',
            'icon' => 'heroicon-o-cloud',
            'provider' => \LBCDev\OAuthManager\Providers\DropboxProvider::class,
            'scopes' => [
                'files.content.write',
                'files.content.read',
                'files.metadata.read'
            ],
            'fields' => [
                'client_id' => env('DROPBOX_CLIENT_ID'),
                'client_secret' => env('DROPBOX_CLIENT_SECRET'),
                'redirect_uri' => env('DROPBOX_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('DROPBOX_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', true),
            ]
        ],
        'mega' => [
            'name' => 'Mega',
            'slug' => 'mega',
            'icon' => 'heroicon-o-cloud-arrow-up',
            'provider' => \LBCDev\OAuthManager\Providers\MegaProvider::class,
            'scopes' => [
                // Mega no usa scopes tradicionales, pero mantenemos la estructura
                'files.read',
                'files.write'
            ],
            'fields' => [
                'email' => env('MEGA_EMAIL'),
                'password' => env('MEGA_PASSWORD'),
                'redirect_uri' => env('MEGA_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('MEGA_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        'youtube' => [
            'name' => 'YouTube',
            'slug' => 'youtube',
            'icon' => 'heroicon-o-play',
            'provider' => \LBCDev\OAuthManager\Providers\YouTubeProvider::class,
            'scopes' => [
                'https://www.googleapis.com/auth/youtube.readonly',
                'https://www.googleapis.com/auth/youtube.upload',
                'https://www.googleapis.com/auth/youtube.force-ssl'
            ],
            'fields' => [
                'client_id' => env('YOUTUBE_CLIENT_ID'),
                'client_secret' => env('YOUTUBE_CLIENT_SECRET'),
                'redirect_uri' => env('YOUTUBE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('YOUTUBE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
    ],

    'callback_route' => 'oauth-manager.callback',
    'middleware' => ['web'],

    // URL de redirección por defecto después del callback OAuth
    // Si no se especifica, se usará redirect()->back()
    'default_redirect_url' => env('OAUTH_MANAGER_DEFAULT_REDIRECT_URL', null),
];
