<?php

namespace LBCDev\OAuthFileExplorer\Tests\Unit;

use LBCDev\OAuthFileExplorer\Tests\TestCase;
use LBCDev\OAuthFileExplorer\Services\Explorers\OneDriveExplorer;
use LBCDev\OAuthManager\Models\OAuthService;
use PHPUnit\Framework\Attributes\Test;

class OneDriveExplorerTest extends TestCase
{
    private OneDriveExplorer $explorer;
    private OAuthService $oauthService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->oauthService = OAuthService::create([
            'name' => 'Test OneDrive',
            'service_type' => 'onedrive',
            'access_token' => 'test_token',
            'is_active' => true,
            'config' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
                'tenant_id' => 'common',
                'redirect_uri' => 'https://test.com/callback'
            ]
        ]);

        $this->explorer = new OneDriveExplorer($this->oauthService);
    }

    #[Test]
    public function it_returns_correct_service_type()
    {
        $this->assertEquals('onedrive', $this->explorer->getServiceType());
    }

    #[Test]
    public function it_returns_supported_mime_types()
    {
        $mimeTypes = $this->explorer->getSupportedMimeTypes();

        $this->assertIsArray($mimeTypes);
        $this->assertContains('application/pdf', $mimeTypes);
        $this->assertContains('image/jpeg', $mimeTypes);
        $this->assertContains('video/mp4', $mimeTypes);
        $this->assertContains('audio/mp3', $mimeTypes);
        $this->assertContains('application/zip', $mimeTypes);
        $this->assertContains('text/plain', $mimeTypes);
    }

    #[Test]
    public function it_supports_specific_mime_types()
    {
        $this->assertTrue($this->explorer->supportsMimeType('application/pdf'));
        $this->assertTrue($this->explorer->supportsMimeType('image/jpeg'));
        $this->assertTrue($this->explorer->supportsMimeType('video/mp4'));
        $this->assertFalse($this->explorer->supportsMimeType('unsupported/type'));
    }

    #[Test]
    public function it_formats_file_data_correctly_for_file()
    {
        // Test will be skipped for now due to Microsoft Graph SDK complexity
        $this->markTestSkipped('Microsoft Graph SDK integration tests require complex mocking');
    }

    #[Test]
    public function it_formats_file_data_correctly_for_folder()
    {
        // Test will be skipped for now due to Microsoft Graph SDK complexity
        $this->markTestSkipped('Microsoft Graph SDK integration tests require complex mocking');
    }

    #[Test]
    public function it_handles_empty_drive_item()
    {
        $reflection = new \ReflectionClass($this->explorer);
        $method = $reflection->getMethod('formatFileData');
        $method->setAccessible(true);

        $result = $method->invoke($this->explorer, null);
        $this->assertEquals([], $result);

        $result = $method->invoke($this->explorer, 'not-a-drive-item');
        $this->assertEquals([], $result);
    }

    #[Test]
    public function it_returns_root_breadcrumb_for_root_folder()
    {
        $breadcrumb = $this->explorer->getBreadcrumb('root');

        $this->assertCount(1, $breadcrumb);
        $this->assertEquals('root', $breadcrumb[0]['id']);
        $this->assertEquals('OneDrive', $breadcrumb[0]['name']);
    }

    #[Test]
    public function it_has_oauth_service()
    {
        $this->assertInstanceOf(OAuthService::class, $this->explorer->getOAuthService());
        $this->assertEquals('onedrive', $this->explorer->getOAuthService()->service_type);
    }

    #[Test]
    public function it_can_set_oauth_service()
    {
        $newService = OAuthService::create([
            'name' => 'Another OneDrive',
            'service_type' => 'onedrive',
            'access_token' => 'another_token',
            'is_active' => true,
        ]);

        $result = $this->explorer->setOAuthService($newService);

        $this->assertInstanceOf(OneDriveExplorer::class, $result);
        $this->assertEquals($newService->id, $this->explorer->getOAuthService()->id);
    }
}
